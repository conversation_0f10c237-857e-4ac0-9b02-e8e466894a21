@echo off
chcp 65001 >nul
title 快手采集工具 - EXE打包

echo.
echo ================================================
echo           快手采集工具 - EXE打包脚本
echo           支持Python 3.13，防止中文乱码
echo ================================================
echo.

REM 设置环境变量防止中文乱码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ 错误：未检测到Python，请先安装Python 3.7或更高版本
    echo.
    pause
    exit /b 1
)

echo ✓ 检测到Python环境
python --version

echo.
echo 正在运行打包脚本...
echo.

REM 运行Python打包脚本
python build_exe.py

echo.
echo 打包脚本执行完成
pause
