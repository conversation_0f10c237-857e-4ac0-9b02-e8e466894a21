# 快手采集工具 - EXE打包说明

## 概述

本脚本用于将快手采集工具打包成独立的EXE可执行文件，支持Python 3.13，并解决中文乱码问题。

## 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: Python 3.7+ (推荐3.13)
- **内存**: 至少4GB RAM
- **磁盘空间**: 至少2GB可用空间

## 快速开始

### 方法一：一键打包（推荐）

1. 双击运行 `打包EXE.bat`
2. 等待自动安装依赖和打包完成
3. 在 `dist` 目录找到生成的 `快手采集工具.exe`

### 方法二：手动打包

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行打包脚本：
   ```bash
   python build_exe.py
   ```

## 文件说明

### 核心文件
- `build_exe.py` - 主打包脚本
- `打包EXE.bat` - 一键打包批处理文件
- `requirements.txt` - 依赖包列表
- `快手采集工具.spec` - PyInstaller配置文件（自动生成）

### 源代码文件
- `main.py` - 主程序入口
- `data_collector.py` - 数据采集模块
- `product_query.py` - 商品查询模块
- `category_parser.py` - 类目解析模块
- `cookie_exporter.py` - Cookie管理模块

### 数据文件
- `data/` - 数据目录
- `logs/` - 日志目录
- `*.md` - 文档文件

## 打包特性

### 防中文乱码配置
- 设置 `PYTHONIOENCODING=utf-8`
- 设置 `PYTHONUTF8=1`
- 使用UTF-8编码处理所有文本
- 批处理文件使用 `chcp 65001` 设置UTF-8代码页

### PyInstaller优化
- **单文件打包**: 生成单个EXE文件，便于分发
- **无控制台窗口**: 纯GUI应用，不显示命令行窗口
- **UPX压缩**: 减小文件体积
- **依赖自动检测**: 自动包含所需的Python库
- **数据文件包含**: 自动打包data和logs目录

### 兼容性处理
- **PyQt版本兼容**: 自动检测并支持PyQt5/PyQt6
- **Python版本兼容**: 支持Python 3.7到3.13
- **依赖版本管理**: 根据Python版本选择合适的依赖

## 打包过程

### 阶段1：环境检查
- 检查Python版本
- 验证必要文件存在

### 阶段2：依赖安装
- 安装PyInstaller
- 安装PyQt (优先PyQt5，失败则PyQt6)
- 安装其他依赖包

### 阶段3：配置生成
- 创建PyInstaller spec文件
- 配置隐藏导入模块
- 设置数据文件包含规则

### 阶段4：执行打包
- 清理旧的构建文件
- 运行PyInstaller
- 验证输出文件

## 输出结果

### 成功打包后的文件结构
```
项目根目录/
├── dist/
│   └── 快手采集工具.exe    # 最终的可执行文件
├── build/                   # 临时构建文件（可删除）
├── 快手采集工具.spec       # PyInstaller配置文件
└── build.bat               # 快速构建脚本
```

### 可执行文件特点
- **文件名**: 快手采集工具.exe
- **大小**: 约100-200MB（包含所有依赖）
- **运行**: 双击即可运行，无需安装Python环境
- **便携性**: 可复制到其他Windows电脑直接运行

## 常见问题

### Q1: 打包失败，提示缺少模块
**解决方案**:
1. 检查requirements.txt中的依赖是否完整
2. 手动安装缺少的模块：`pip install 模块名`
3. 在spec文件的hiddenimports中添加缺少的模块

### Q2: 生成的EXE文件过大
**解决方案**:
1. 启用UPX压缩（已默认开启）
2. 在spec文件的excludes中添加不需要的模块
3. 使用虚拟环境减少不必要的依赖

### Q3: 运行EXE时出现中文乱码
**解决方案**:
1. 确保系统区域设置为中文
2. 检查EXE文件是否在中文路径下
3. 重新打包，确保使用UTF-8编码

### Q4: PyQt版本冲突
**解决方案**:
1. 卸载所有PyQt版本：`pip uninstall PyQt5 PyQt6 PyQtWebEngine PyQt6-WebEngine`
2. 重新运行打包脚本，让其自动选择合适版本

### Q5: Python 3.13兼容性问题
**解决方案**:
1. 确保使用最新版本的PyInstaller (>=6.0.0)
2. 如果仍有问题，可降级到Python 3.11或3.12

## 高级配置

### 自定义图标
在spec文件中修改：
```python
exe = EXE(
    # ...
    icon='path/to/your/icon.ico',  # 添加图标路径
    # ...
)
```

### 添加版本信息
1. 创建version.txt文件
2. 在spec文件中添加：`version_file='version.txt'`

### 优化启动速度
1. 减少hiddenimports中的模块
2. 使用--onedir模式而非--onefile
3. 排除不必要的数据文件

## 技术支持

如遇到问题，请检查：
1. Python版本是否支持
2. 依赖包是否正确安装
3. 源代码文件是否完整
4. 系统环境变量设置

## 更新日志

- **v1.0.0** (2024-01-20)
  - 初始版本
  - 支持Python 3.7-3.13
  - 防中文乱码配置
  - 自动依赖管理
  - PyQt版本兼容
