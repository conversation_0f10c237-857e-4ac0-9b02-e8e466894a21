# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目根目录
project_root = Path.cwd()

# 数据文件列表
datas = [
    (str(project_root / "data"), "data"),
    (str(project_root / "logs"), "logs"),
    (str(project_root / "*.md"), "."),
]

# 隐藏导入模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebEngineCore',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets', 
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'requests',
    'pandas',
    'openpyxl',
    'json',
    'pathlib',
    'datetime',
    'threading',
    'concurrent.futures',
    'logging',
    'urllib.parse',
    'subprocess',
    'time',
    're'
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'IPython',
    'jupyter'
]

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 去除重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='快手采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
    # 防止中文乱码的关键配置
    manifest=None,
    # 设置编码
    bootloader_ignore_signals=False,
)
