#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类目解析模块
解析类目响应数据.md文件，生成树形结构的Category data.txt文件
同时支持类目数据补充功能
"""

import json
import re
from pathlib import Path


class CategoryParser:
    """类目解析器"""

    def __init__(self):
        self.category_tree = {}
        self.category_mapping = {}  # 存储key到名称的映射
        
    def parse_category_file(self, file_path):
        """解析类目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析JSON数据
            data = json.loads(content)
            
            # 提取类目数据
            if 'data' in data and 'sytWebItemTopRank' in data['data']:
                for item in data['data']['sytWebItemTopRank']:
                    if item.get('code') == 'itemCategory' and 'list' in item:
                        self.parse_category_list(item['list'])
                        
            return True
            
        except Exception as e:
            print(f"解析类目文件失败: {e}")
            return False
            
    def parse_category_list(self, category_list):
        """解析类目列表"""
        for category in category_list:
            if category.get('hierarchy') == 0:  # 一级类目
                first_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': category.get('categoryPid', ''),
                    'children': {}
                }
                self.category_tree[category['label']] = first_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_second_level(category['children'], category['label'], category['key'])

    def parse_second_level(self, children, first_level, first_key):
        """解析二级类目"""
        for category in children:
            if category.get('hierarchy') == 1:  # 二级类目
                second_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': first_key,  # 使用first_key作为父类目ID
                    'children': {}
                }
                self.category_tree[first_level]['children'][category['label']] = second_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_third_level(category['children'], first_level, category['label'], category['key'])

    def parse_third_level(self, children, first_level, second_level, second_key):
        """解析三级类目"""
        for category in children:
            if category.get('hierarchy') == 2:  # 三级类目
                third_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': second_key,
                    'children': []
                }
                self.category_tree[first_level]['children'][second_level]['children'][category['label']] = third_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_fourth_level(category['children'], first_level, second_level, category['label'], category['key'])

    def parse_fourth_level(self, children, first_level, second_level, third_level, third_key):
        """解析四级类目"""
        for category in children:
            if category.get('hierarchy') == 3:  # 四级类目
                fourth_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': third_key
                }
                self.category_tree[first_level]['children'][second_level]['children'][third_level]['children'].append(fourth_level_data)
                self.category_mapping[category['key']] = category['label']
                
    def save_category_data(self, output_path):
        """保存类目数据到文件"""
        try:
            # 确保data目录存在
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.category_tree, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"保存类目数据失败: {e}")
            return False
            
    def print_tree_structure(self):
        """打印树形结构（用于调试）"""
        for first_level, first_data in self.category_tree.items():
            print(f"{first_level}（一级类目 | categoryPid: {first_data['categoryPid']}）")

            for second_level, second_data in first_data['children'].items():
                print(f"└── {second_level}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

                for third_level, third_data in second_data['children'].items():
                    print(f"  └── {third_level}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                    for fourth_data in third_data['children']:
                        print(f"    ├── {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

    def print_tree_structure_sample(self):
        """打印树形结构示例（只显示第一个分支）"""
        if not self.category_tree:
            return

        # 只显示第一个一级类目的完整结构
        first_level_name = list(self.category_tree.keys())[0]
        first_data = self.category_tree[first_level_name]

        print(f"{first_level_name}（一级类目 | categoryPid: {first_data['categoryPid']}）")

        # 只显示第一个二级类目
        if first_data['children']:
            second_level_name = list(first_data['children'].keys())[0]
            second_data = first_data['children'][second_level_name]

            print(f"└── {second_level_name}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

            # 只显示第一个三级类目
            if second_data['children']:
                third_level_name = list(second_data['children'].keys())[0]
                third_data = second_data['children'][third_level_name]

                print(f"  └── {third_level_name}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                # 显示前几个四级类目
                for i, fourth_data in enumerate(third_data['children'][:3]):
                    symbol = "├──" if i < 2 else "└──"
                    print(f"    {symbol} {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

                if len(third_data['children']) > 3:
                    print(f"    └── ... 还有 {len(third_data['children']) - 3} 个四级类目")
                        
    def get_category_count(self):
        """获取类目统计信息"""
        first_count = len(self.category_tree)
        second_count = sum(len(first_data['children']) for first_data in self.category_tree.values())
        third_count = sum(
            len(second_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
        )
        fourth_count = sum(
            len(third_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
            for third_data in second_data['children'].values()
        )

        return {
            'first_level': first_count,
            'second_level': second_count,
            'third_level': third_count,
            'fourth_level': fourth_count
        }


class CategorySupplementTool:
    """类目数据补充工具（高性能版本）"""

    def __init__(self, category_data_file="data/Category data.txt",
                 supplement_data_file="类目补充响应数据.md"):
        """
        初始化类目补充工具

        Args:
            category_data_file: 原始类目数据文件路径
            supplement_data_file: 补充类目数据文件路径
        """
        self.category_data_file = category_data_file
        self.supplement_data_file = supplement_data_file
        self.category_data = {}
        self.supplement_data = {}

        # 高性能缓存
        self.key_to_node_map = {}  # key -> 节点引用的映射
        self.existing_keys = set()  # 已存在的key集合

    def load_category_data(self):
        """加载原始类目数据并构建高性能索引"""
        try:
            with open(self.category_data_file, 'r', encoding='utf-8') as f:
                self.category_data = json.load(f)
            print(f"✓ 成功加载原始类目数据: {self.category_data_file}")

            # 构建高性能索引
            self._build_performance_index()
            print(f"✓ 构建性能索引完成，索引了 {len(self.existing_keys)} 个key")

            return self.category_data
        except Exception as e:
            print(f"✗ 加载原始类目数据失败: {e}")
            return {}

    def _build_performance_index(self):
        """构建高性能索引：key -> 节点引用映射"""
        self.key_to_node_map.clear()
        self.existing_keys.clear()

        def index_recursive(data, parent_ref=None):
            """递归构建索引"""
            if isinstance(data, dict):
                # 如果有key，建立索引
                if 'key' in data:
                    key = data['key']
                    self.existing_keys.add(key)
                    self.key_to_node_map[key] = data

                # 递归处理children
                if 'children' in data:
                    if isinstance(data['children'], dict):
                        for child_data in data['children'].values():
                            index_recursive(child_data, data)
                    elif isinstance(data['children'], list):
                        for child_data in data['children']:
                            index_recursive(child_data, data)

                # 递归处理其他字典值
                for key, value in data.items():
                    if key not in ['key', 'name', 'categoryPid'] and isinstance(value, dict):
                        index_recursive(value, data)

        # 从根节点开始构建索引
        index_recursive(self.category_data)

    def load_supplement_data(self):
        """加载补充类目数据"""
        try:
            with open(self.supplement_data_file, 'r', encoding='utf-8') as f:
                self.supplement_data = json.load(f)
            print(f"✓ 成功加载补充类目数据: {self.supplement_data_file}")
            return self.supplement_data
        except Exception as e:
            print(f"✗ 加载补充类目数据失败: {e}")
            return {}

    def extract_all_keys_from_original(self):
        """从原始文件中提取所有的key值（高性能版本）"""
        # 直接使用已构建的索引，无需重新解析文件
        print(f"从索引中获取到 {len(self.existing_keys)} 个key")
        return self.existing_keys

    def extract_categories_from_supplement(self):
        """从补充文件中提取所有类目（高性能版本，只处理hierarchy 1-3）"""
        categories = []
        try:
            # 使用栈代替递归，避免深度递归的性能问题
            stack = [(cat, 0) for cat in self.supplement_data.get('categoryList', [])]

            while stack:
                cat, depth = stack.pop()
                hierarchy = cat.get('hierarchy', 0)

                # 只处理hierarchy 1-3的类目
                if hierarchy <= 3:
                    categories.append({
                        'categoryId': str(cat.get('categoryId', '')),
                        'categoryName': cat.get('categoryName', ''),
                        'categoryPid': str(cat.get('categoryPid', '')),
                        'hierarchy': hierarchy
                    })

                # 只在hierarchy < 3且深度不超过3时继续处理子类目
                if hierarchy < 3 and depth < 3 and 'childCategory' in cat and cat['childCategory']:
                    # 将子类目添加到栈中，深度+1
                    for child_cat in cat['childCategory']:
                        stack.append((child_cat, depth + 1))

            print(f"从补充文件中提取到 {len(categories)} 个类目（仅hierarchy 1-3）")
            return categories
        except Exception as e:
            print(f"提取补充文件类目失败: {e}")
            return []

    def find_missing_categories(self, original_keys, supplement_categories):
        """找出缺失的类目"""
        missing = []
        for cat in supplement_categories:
            if cat['categoryId'] not in original_keys:
                missing.append(cat)
        return missing

    def find_parent_in_data(self, data, parent_key, current_path=""):
        """高性能查找父类目（使用索引）"""
        # 直接从索引中获取节点引用，O(1)时间复杂度
        parent_data = self.key_to_node_map.get(parent_key)
        if parent_data:
            return parent_data, f"indexed:{parent_key}"
        return None, ""

    def find_parent_fast(self, parent_key):
        """超高速查找父类目（简化版本）"""
        return self.key_to_node_map.get(parent_key)

    def add_categories_to_original(self, missing_categories):
        """将缺失的类目添加到原始文件中"""
        try:
            # 创建备份
            backup_file = self.category_data_file + '.backup'
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.category_data, f, ensure_ascii=False, indent=2)
            print(f"创建备份文件: {backup_file}")

            added_count = 0

            # 只处理hierarchy 1-3的类目，按hierarchy顺序处理
            valid_hierarchies = [h for h in sorted(set(cat['hierarchy'] for cat in missing_categories)) if h <= 3]

            for hierarchy in valid_hierarchies:
                cats_for_hierarchy = [cat for cat in missing_categories if cat['hierarchy'] == hierarchy]
                print(f"   处理hierarchy={hierarchy}的{len(cats_for_hierarchy)}个类目...")

                # 高性能批量处理
                batch_size = 100
                for i, cat in enumerate(cats_for_hierarchy):
                    parent_key = cat['categoryPid']

                    # 使用高速查找父类目
                    parent_data = self.find_parent_fast(parent_key)

                    if parent_data:
                        # 根据父类目的children类型决定添加方式
                        if 'children' not in parent_data:
                            parent_data['children'] = []

                        new_category = {
                            "name": cat['categoryName'],
                            "key": cat['categoryId'],
                            "categoryPid": parent_key
                        }

                        # 如果父类目的children是字典类型，添加为字典项
                        if isinstance(parent_data['children'], dict):
                            new_category['children'] = {}
                            parent_data['children'][cat['categoryName']] = new_category
                        # 如果是列表类型，添加为列表项
                        elif isinstance(parent_data['children'], list):
                            parent_data['children'].append(new_category)

                        # 立即更新索引，确保后续查找能找到新添加的节点
                        self.key_to_node_map[cat['categoryId']] = new_category
                        self.existing_keys.add(cat['categoryId'])

                        added_count += 1

                        # 减少输出频率，提高性能
                        if i % batch_size == 0 or i == len(cats_for_hierarchy) - 1:
                            print(f"     ✓ 已处理 {i + 1}/{len(cats_for_hierarchy)} 个类目")
                    else:
                        print(f"     ✗ 未找到父类目: {cat['categoryName']} (父ID: {parent_key})")

            # 保存更新后的数据
            with open(self.category_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.category_data, f, ensure_ascii=False, indent=2)

            print(f"总共添加了 {added_count} 个类目")
            return True

        except Exception as e:
            print(f"添加类目失败: {e}")
            return False

    def validate_json_format(self):
        """验证JSON格式的有效性"""
        try:
            # 重新加载验证
            with open(self.category_data_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print("✓ JSON格式验证通过")
            return True
        except Exception as e:
            print(f"✗ JSON格式验证失败: {e}")
            return False

    def run_supplement(self):
        """执行完整的类目补充流程"""
        print("=" * 50)
        print("开始执行类目数据补充...")
        print("=" * 50)

        # 1. 加载数据
        if not self.load_category_data() or not self.load_supplement_data():
            return False

        # 2. 提取原始数据中的所有key
        original_keys = self.extract_all_keys_from_original()

        # 3. 提取补充数据中的所有类目
        supplement_categories = self.extract_categories_from_supplement()

        # 4. 找出缺失的类目
        missing_categories = self.find_missing_categories(original_keys, supplement_categories)

        # 过滤掉hierarchy 4的类目
        filtered_missing = [cat for cat in missing_categories if cat['hierarchy'] <= 3]

        print(f"\n发现 {len(missing_categories)} 个缺失的类目")
        print(f"过滤后需要处理 {len(filtered_missing)} 个类目（仅hierarchy 1-3）")

        # 按hierarchy分组显示
        hierarchy_groups = {}
        for cat in filtered_missing:
            h = cat['hierarchy']
            if h not in hierarchy_groups:
                hierarchy_groups[h] = []
            hierarchy_groups[h].append(cat)

        for hierarchy, cats in sorted(hierarchy_groups.items()):
            print(f"Hierarchy {hierarchy}: {len(cats)} 个")

        # 更新missing_categories为过滤后的列表
        missing_categories = filtered_missing

        # 5. 如果有缺失类目，进行补充
        if missing_categories:
            print(f"\n开始补充 {len(missing_categories)} 个缺失类目...")

            # 添加缺失的类目
            if not self.add_categories_to_original(missing_categories):
                return False

            # 验证JSON格式
            if not self.validate_json_format():
                return False

            print("\n✅ 类目补充完成！")
            return True
        else:
            print("\n✅ 没有发现缺失的类目，数据已经是最新的！")
            return True


def parse_categories():
    """解析类目的主函数"""
    parser = CategoryParser()

    # 输入文件路径
    input_file = Path("类目响应数据.md")

    # 输出文件路径
    output_dir = Path("data")
    output_file = output_dir / "Category data.txt"

    if not input_file.exists():
        print(f"错误：未找到文件 {input_file}")
        return False

    print("开始解析类目数据...")

    # 解析文件
    if not parser.parse_category_file(input_file):
        print("解析失败")
        return False

    # 保存数据
    if not parser.save_category_data(output_file):
        print("保存失败")
        return False

    # 获取统计信息
    stats = parser.get_category_count()

    print(f"解析完成！")
    print(f"一级类目: {stats['first_level']} 个")
    print(f"二级类目: {stats['second_level']} 个")
    print(f"三级类目: {stats['third_level']} 个")
    print(f"四级类目: {stats['fourth_level']} 个")
    print(f"数据已保存到: {output_file}")

    # 打印部分树形结构示例
    print("\n树形结构示例:")
    parser.print_tree_structure_sample()

    # 检查是否存在补充数据文件，如果存在则进行类目补充
    supplement_file = Path("类目补充响应数据.md")
    if supplement_file.exists():
        print(f"\n发现补充数据文件: {supplement_file}")
        print("开始执行类目数据补充...")

        # 创建补充工具并执行补充
        supplement_tool = CategorySupplementTool()
        if supplement_tool.run_supplement():
            print("类目数据补充成功完成！")
        else:
            print("类目数据补充失败，但基础解析已完成")
    else:
        print(f"\n未发现补充数据文件: {supplement_file}")
        print("跳过类目补充步骤")

    return True


if __name__ == "__main__":
    parse_categories()
