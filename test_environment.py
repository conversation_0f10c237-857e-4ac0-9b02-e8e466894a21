#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本 - 检查打包环境是否就绪
"""

import sys
import importlib
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("=== Python版本检查 ===")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✓ Python版本符合要求")
        return True
    else:
        print("✗ Python版本不符合要求，需要3.7或更高版本")
        return False

def test_required_modules():
    """测试必需模块"""
    print("\n=== 必需模块检查 ===")
    
    # 必需模块列表
    required_modules = [
        ('requests', '网络请求库'),
        ('pandas', '数据处理库'),
        ('openpyxl', 'Excel处理库'),
        ('pathlib', '路径处理库'),
    ]
    
    # PyQt模块（任一可用即可）
    pyqt_modules = [
        ('PyQt5.QtWidgets', 'PyQt5 GUI库'),
        ('PyQt6.QtWidgets', 'PyQt6 GUI库'),
    ]
    
    all_passed = True
    
    # 检查基础模块
    for module_name, description in required_modules:
        try:
            importlib.import_module(module_name)
            print(f"✓ {description} ({module_name})")
        except ImportError:
            print(f"✗ {description} ({module_name}) - 未安装")
            all_passed = False
    
    # 检查PyQt模块
    pyqt_available = False
    for module_name, description in pyqt_modules:
        try:
            importlib.import_module(module_name)
            print(f"✓ {description} ({module_name})")
            pyqt_available = True
            break
        except ImportError:
            continue
    
    if not pyqt_available:
        print("✗ PyQt库 - 未安装 (需要PyQt5或PyQt6)")
        all_passed = False
    
    return all_passed

def test_pyinstaller():
    """测试PyInstaller"""
    print("\n=== PyInstaller检查 ===")
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        return False

def test_source_files():
    """测试源文件"""
    print("\n=== 源文件检查 ===")
    
    required_files = [
        'main.py',
        'data_collector.py',
        'product_query.py', 
        'category_parser.py',
        'cookie_exporter.py'
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists():
            print(f"✓ {file_name}")
        else:
            print(f"✗ {file_name} - 文件不存在")
            all_exist = False
    
    return all_exist

def test_directories():
    """测试目录结构"""
    print("\n=== 目录结构检查 ===")
    
    required_dirs = ['data', 'logs']
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✓ {dir_name}/ 目录存在")
        else:
            print(f"! {dir_name}/ 目录不存在，将自动创建")
            dir_path.mkdir(exist_ok=True)
    
    return True

def test_encoding():
    """测试编码支持"""
    print("\n=== 编码支持检查 ===")
    
    try:
        # 测试中文字符
        test_string = "快手采集工具测试"
        encoded = test_string.encode('utf-8')
        decoded = encoded.decode('utf-8')
        
        if test_string == decoded:
            print("✓ UTF-8编码支持正常")
            return True
        else:
            print("✗ UTF-8编码支持异常")
            return False
    except Exception as e:
        print(f"✗ 编码测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("快手采集工具 - 环境测试")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("必需模块", test_required_modules),
        ("PyInstaller", test_pyinstaller),
        ("源文件", test_source_files),
        ("目录结构", test_directories),
        ("编码支持", test_encoding),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 环境检查完全通过！可以开始打包。")
        print("运行命令: python build_exe.py")
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，请先解决问题。")
        print("建议运行: pip install -r requirements.txt")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
    input("\n按任意键退出...")
