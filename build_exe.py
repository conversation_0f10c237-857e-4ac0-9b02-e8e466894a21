#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - EXE打包脚本
支持Python 3.13，防止中文乱码
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 7:
        print("错误：需要Python 3.7或更高版本")
        return False
    
    if version.minor == 13:
        print("✓ 检测到Python 3.13，将使用兼容配置")
    
    return True

def install_dependencies():
    """安装必要的依赖包"""
    print("\n=== 安装依赖包 ===")
    
    # 基础依赖
    base_packages = [
        "pyinstaller>=6.0.0",
        "requests>=2.25.0",
        "pandas>=1.3.0",
        "openpyxl>=3.0.0",
        "pathlib2;python_version<'3.4'"
    ]
    
    # PyQt依赖 - 优先尝试PyQt5，失败则安装PyQt6
    pyqt_packages = [
        ["PyQt5>=5.15.0", "PyQtWebEngine>=5.15.0"],
        ["PyQt6>=6.0.0", "PyQt6-WebEngine>=6.0.0"]
    ]
    
    # 安装基础依赖
    for package in base_packages:
        try:
            print(f"安装 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True, text=True, encoding='utf-8')
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安装失败: {e}")
            return False
    
    # 安装PyQt依赖
    pyqt_installed = False
    for pyqt_group in pyqt_packages:
        try:
            for package in pyqt_group:
                print(f"安装 {package}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True, text=True, encoding='utf-8')
            print(f"✓ PyQt 依赖安装成功")
            pyqt_installed = True
            break
        except subprocess.CalledProcessError:
            print(f"✗ {pyqt_group[0]} 安装失败，尝试下一个版本...")
            continue
    
    if not pyqt_installed:
        print("错误：无法安装PyQt依赖")
        return False
    
    return True

def create_spec_file():
    """创建PyInstaller spec文件"""
    print("\n=== 创建PyInstaller配置文件 ===")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目根目录
project_root = Path.cwd()

# 数据文件列表
datas = [
    (str(project_root / "data"), "data"),
    (str(project_root / "logs"), "logs"),
    (str(project_root / "*.md"), "."),
]

# 隐藏导入模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebEngineCore',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets', 
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'requests',
    'pandas',
    'openpyxl',
    'json',
    'pathlib',
    'datetime',
    'threading',
    'concurrent.futures',
    'logging',
    'urllib.parse',
    'subprocess',
    'time',
    're'
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy.distutils',
    'scipy',
    'IPython',
    'jupyter'
]

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 去除重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='快手采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
    # 防止中文乱码的关键配置
    manifest=None,
    # 设置编码
    bootloader_ignore_signals=False,
)
'''
    
    spec_file = Path("快手采集工具.spec")
    try:
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        print(f"✓ 配置文件已创建: {spec_file}")
        return spec_file
    except Exception as e:
        print(f"✗ 创建配置文件失败: {e}")
        return None

def create_build_script():
    """创建构建脚本"""
    print("\n=== 创建构建脚本 ===")
    
    build_script = '''@echo off
chcp 65001 >nul
echo 开始构建快手采集工具...
echo.

REM 设置环境变量防止中文乱码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM 清理之前的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 正在打包，请稍候...
python -m PyInstaller --clean "快手采集工具.spec"

if %errorlevel% equ 0 (
    echo.
    echo ✓ 打包成功！
    echo 可执行文件位置: dist\\快手采集工具.exe
    echo.
    pause
) else (
    echo.
    echo ✗ 打包失败！
    echo 请检查错误信息并重试
    echo.
    pause
)
'''
    
    build_bat = Path("build.bat")
    try:
        with open(build_bat, 'w', encoding='utf-8') as f:
            f.write(build_script)
        print(f"✓ 构建脚本已创建: {build_bat}")
        return build_bat
    except Exception as e:
        print(f"✗ 创建构建脚本失败: {e}")
        return None

def prepare_data_files():
    """准备数据文件"""
    print("\n=== 准备数据文件 ===")
    
    # 确保data目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 确保logs目录存在
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 检查必要文件
    required_files = [
        "main.py",
        "data_collector.py", 
        "product_query.py",
        "category_parser.py",
        "cookie_exporter.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"✗ 缺少必要文件: {missing_files}")
        return False
    
    print("✓ 所有必要文件检查完成")
    return True

def build_exe():
    """执行打包"""
    print("\n=== 开始打包 ===")
    
    # 设置环境变量防止中文乱码
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    try:
        # 清理之前的构建文件
        for dir_name in ['build', 'dist', '__pycache__']:
            if Path(dir_name).exists():
                shutil.rmtree(dir_name)
                print(f"✓ 清理目录: {dir_name}")
        
        # 执行PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller", 
            "--clean", 
            "快手采集工具.spec"
        ]
        
        print("正在执行打包命令...")
        print(f"命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd, 
            env=env,
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            cwd=Path.cwd()
        )
        
        if result.returncode == 0:
            print("✓ 打包成功！")
            
            # 检查输出文件
            exe_file = Path("dist") / "快手采集工具.exe"
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"✓ 可执行文件: {exe_file}")
                print(f"✓ 文件大小: {size_mb:.1f} MB")
                return True
            else:
                print("✗ 未找到生成的可执行文件")
                return False
        else:
            print("✗ 打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("快手采集工具 - EXE打包脚本")
    print("支持Python 3.13，防止中文乱码")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按任意键退出...")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("\n依赖安装失败，请手动安装后重试")
        input("按任意键退出...")
        return
    
    # 准备数据文件
    if not prepare_data_files():
        print("\n数据文件准备失败")
        input("按任意键退出...")
        return
    
    # 创建配置文件
    spec_file = create_spec_file()
    if not spec_file:
        print("\n配置文件创建失败")
        input("按任意键退出...")
        return
    
    # 创建构建脚本
    build_bat = create_build_script()
    if not build_bat:
        print("\n构建脚本创建失败")
        input("按任意键退出...")
        return
    
    # 执行打包
    if build_exe():
        print("\n" + "=" * 50)
        print("✓ 打包完成！")
        print("可执行文件位置: dist\\快手采集工具.exe")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("✗ 打包失败！")
        print("请检查错误信息并重试")
        print("=" * 50)
    
    input("\n按任意键退出...")

if __name__ == "__main__":
    main()
