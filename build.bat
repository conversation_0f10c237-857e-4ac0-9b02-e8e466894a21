@echo off
chcp 65001 >nul
echo 开始构建快手采集工具...
echo.

REM 设置环境变量防止中文乱码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM 清理之前的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 正在打包，请稍候...
python -m PyInstaller --clean "快手采集工具.spec"

if %errorlevel% equ 0 (
    echo.
    echo ✓ 打包成功！
    echo 可执行文件位置: dist\快手采集工具.exe
    echo.
    pause
) else (
    echo.
    echo ✗ 打包失败！
    echo 请检查错误信息并重试
    echo.
    pause
)
